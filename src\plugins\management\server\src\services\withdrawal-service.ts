import type { Core } from '@strapi/strapi';

const withdrawalService = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listWithdrawals(query: any) {
    // Implementation will be copied from withraw plugin
    return { data: [], pagination: { page: 1, pageSize: 10, pageCount: 0, total: 0 } };
  },

  async getWithdrawalDetail(id: number) {
    // Implementation will be copied from withraw plugin
    return { id, amount: 0 };
  },

  async approveWithdrawal(id: number, adminUser: any) {
    // Implementation will be copied from withraw plugin
    return { id, status: 'approved', adminUser };
  },

  async rejectWithdrawal(id: number, reason: string, adminUser: any) {
    // Implementation will be copied from withraw plugin
    return { id, status: 'rejected', reason, adminUser };
  },

  async getWithdrawalStatistics(query: any) {
    // Implementation will be copied from withraw plugin
    return { totalWithdrawals: 0, totalAmount: 0 };
  },
});

export default withdrawalService;
