{"kind": "collectionType", "collectionName": "rut_tiens", "info": {"singularName": "rut-tien", "pluralName": "rut-tiens", "displayName": "<PERSON><PERSON><PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "amount": {"type": "decimal", "required": true}, "statusPaid": {"type": "enumeration", "required": true, "default": "Pending", "enum": ["Pending", "Paid", "Canceled"]}, "paidAt": {"type": "datetime", "required": true}}}