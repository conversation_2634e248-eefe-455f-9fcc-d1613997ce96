import React, { useState, useEffect } from 'react';
import {
  Table as AntTable,
  <PERSON><PERSON> as AntButton,
  Input,
  Select,
  Space,
  Tag,
  Avatar,
  Row,
  Col,
  Tooltip,
  message,
  Empty,
  Spin,
  DatePicker,
  Modal,
  Form,
  InputNumber,
} from 'antd';
import { ReloadOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  SelectInput,
  StyledTable,
} from '../components/shared';
import { Users, DollarSign, TrendingUp, Award, Download } from 'lucide-react';
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

// Types
interface Commission {
  id: number;
  amount: number;
  percentage: number;
  status: 'pending' | 'paid' | 'cancelled';
  type: 'referral' | 'company';
  user: {
    id: number;
    name: string;
    phone: string;
    email: string;
    avatar?: string;
  };
  order: {
    id: number;
    code: string;
    priceAfterTax: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface CommissionListResponse {
  data: Commission[];
  meta: {
    pagination: {
      total: number;
      page: number;
      pageSize: number;
      pageCount: number;
    };
  };
}

interface Statistics {
  totalCommissions: number;
  pendingCommissions: number;
  approvedCommissions: number;
  paidCommissions: number;
  totalAmount: number;
  pendingAmount: number;
  approvedAmount: number;
  paidAmount: number;
}

const CommissionList: React.FC = () => {
  const { get, put } = useFetchClient();

  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // Edit modal states
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [selectedCommission, setSelectedCommission] = useState<Commission | null>(null);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [editForm] = Form.useForm();
  const [calculatedAmount, setCalculatedAmount] = useState(0);

  const PAGE_SIZE = 10;

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng hoa hồng',
      value: statistics?.totalCommissions || 0,
      icon: Award,
      color: '#3b82f6',
    },
    {
      title: 'Chờ duyệt',
      value: statistics?.pendingCommissions || 0,
      icon: Users,
      color: '#f59e0b',
    },
    {
      title: 'Đã thanh toán',
      value: statistics?.paidCommissions || 0,
      icon: TrendingUp,
      color: '#10b981',
    },
    {
      title: 'Tổng tiền',
      value: statistics?.totalAmount
        ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
            statistics.totalAmount
          )
        : '0 ₫',
      icon: DollarSign,
      color: '#8b5cf6',
    },
  ];

  const fetchCommissions = async (
    currentPage = page,
    currentSearch = search,
    currentStatus = status,
    currentDateRange = dateRange
  ) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: PAGE_SIZE.toString(),
        ...(currentSearch && { search: currentSearch }),
        ...(currentStatus && { status: currentStatus }),
        ...(currentDateRange &&
          currentDateRange[0] && { startDate: currentDateRange[0].format('YYYY-MM-DD') }),
        ...(currentDateRange &&
          currentDateRange[1] && { endDate: currentDateRange[1].format('YYYY-MM-DD') }),
      });

      const response = await get<CommissionListResponse>(`/management/commissions?${queryParams}`);

      setCommissions(response.data.data);
      setTotal(response.data.meta.pagination.total);
    } catch (error) {
      console.error('Error fetching commissions:', error);
      message.error('Không thể tải danh sách hoa hồng');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await get<Statistics>('/management/commissions/statistics');
      setStatistics(response.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  // Handle filter change
  const handleFilterChange = (filterType: string, value: string) => {
    if (filterType === 'status') {
      setStatus(value);
    }
    setPage(1);
  };

  // Handle date range change
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
    setPage(1);
  };

  // Create and download Excel file
  const createAndDownloadExcel = (commissions: Commission[]) => {
    if (!commissions.length) {
      message.warning('Không có dữ liệu để xuất');
      return;
    }

    // Prepare Excel data
    const excelData = commissions.map((commission, index) => ({
      STT: index + 1,
      ID: commission.id,
      'Mã đơn hàng': commission.order.code,
      'Người nhận hoa hồng':
        commission.type === 'referral' ? commission.user?.name || 'N/A' : 'Công ty',
      'Số điện thoại': commission.type === 'referral' ? commission.user?.phone || 'N/A' : 'N/A',
      Email: commission.type === 'referral' ? commission.user?.email || 'N/A' : 'N/A',
      'Loại hoa hồng': commission.type === 'referral' ? 'Giới thiệu' : 'Công ty',
      'Tỷ lệ (%)': commission.percentage,
      'Số tiền hoa hồng': new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(commission.amount),
      'Giá trị đơn hàng': new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(commission.order.priceAfterTax),
      'Trạng thái':
        commission.status === 'pending'
          ? 'Chờ duyệt'
          : commission.status === 'paid'
            ? 'Đã thanh toán'
            : 'Đã hủy',
      'Ngày tạo': new Date(commission.createdAt).toLocaleString('vi-VN'),
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 5 }, // STT
      { wch: 8 }, // ID
      { wch: 20 }, // Mã đơn hàng
      { wch: 25 }, // Người nhận hoa hồng
      { wch: 15 }, // Số điện thoại
      { wch: 30 }, // Email
      { wch: 15 }, // Loại hoa hồng
      { wch: 10 }, // Tỷ lệ
      { wch: 20 }, // Số tiền hoa hồng
      { wch: 20 }, // Giá trị đơn hàng
      { wch: 15 }, // Trạng thái
      { wch: 20 }, // Ngày tạo
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Lịch sử hoa hồng');

    // Generate filename with current date
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    const filename = `lich-su-hoa-hong-${dateStr}-${timeStr}.xlsx`;

    // Write and download file
    XLSX.writeFile(workbook, filename);
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Prepare query params for export (same as current filters)
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '10000', // Get all commissions
        ...(search && { search }),
        ...(status && { status }),
        ...(dateRange && dateRange[0] && { startDate: dateRange[0].format('YYYY-MM-DD') }),
        ...(dateRange && dateRange[1] && { endDate: dateRange[1].format('YYYY-MM-DD') }),
      });

      // Fetch all commissions with current filters
      const response = await get<CommissionListResponse>(`/management/commissions?${queryParams}`);
      const commissionsToExport = response.data.data || [];

      if (commissionsToExport.length === 0) {
        message.warning('Không có hoa hồng nào phù hợp với bộ lọc đã chọn');
        return;
      }

      // Create and download Excel file
      createAndDownloadExcel(commissionsToExport);
      message.success(
        `Đã tải xuống file Excel với ${commissionsToExport.length} bản ghi hoa hồng!`
      );
    } catch (error) {
      console.error('Error exporting commissions:', error);
      message.error('Không thể xuất file Excel');
    }
  };

  useEffect(() => {
    fetchCommissions();
    fetchStatistics();
  }, [page, search, status, dateRange]);

  // Handle edit commission
  const handleEditCommission = (commission: Commission) => {
    setSelectedCommission(commission);
    const orderTotal = commission.order.priceAfterTax || 0;
    const initialAmount = Math.round((orderTotal * commission.percentage) / 100);
    setCalculatedAmount(initialAmount);

    editForm.setFieldsValue({
      percentage: commission.percentage,
      reason: '',
    });
    setEditModalVisible(true);
  };

  // Calculate amount when percentage changes
  const handlePercentageChange = (value: number | null) => {
    if (value && selectedCommission?.order.priceAfterTax) {
      const newAmount = Math.round((selectedCommission.order.priceAfterTax * value) / 100);
      setCalculatedAmount(newAmount);
    } else {
      setCalculatedAmount(0);
    }
  };

  // Handle edit form submit
  const handleEditSubmit = async (values: any) => {
    if (!selectedCommission) return;

    try {
      await put(`/management/commissions/${selectedCommission.id}`, {
        percentage: values.percentage,
        reason: values.reason,
      } as any);

      message.success('Cập nhật hoa hồng thành công');
      setEditModalVisible(false);
      fetchCommissions();
      fetchStatistics();
    } catch (error) {
      console.error('Error updating commission:', error);
      message.error('Không thể cập nhật hoa hồng');
    }
  };

  // Handle view history
  const handleViewHistory = async (commission: Commission) => {
    setSelectedCommission(commission);
    try {
      const response = await get(`/management/commissions/${commission.id}/audit-logs`);
      setAuditLogs(response.data.data || []);
      setHistoryModalVisible(true);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      // Show empty history instead of error if audit table doesn't exist
      setAuditLogs([]);
      setHistoryModalVisible(true);
      message.warning(
        'Lịch sử thay đổi chưa khả dụng. Vui lòng restart server để kích hoạt tính năng này.'
      );
    }
  };

  // Table columns
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (id: number) => (
        <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>#{id}</span>
      ),
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: ['order', 'code'],
      key: 'orderCode',
      width: 150,
      render: (code: string) => (
        <span
          style={{ fontFamily: "'Be Vietnam Pro', sans-serif", fontWeight: 500, color: '#1f2937' }}
        >
          {code}
        </span>
      ),
    },
    {
      title: 'Người nhận hoa hồng',
      dataIndex: 'user',
      key: 'user',
      width: 220,
      render: (user: any, record: Commission) => {
        if (record.type === 'company') {
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Avatar size="small" style={{ backgroundColor: '#10b981' }}>
                C
              </Avatar>
              <div>
                <div
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    fontWeight: 500,
                    color: '#1f2937',
                  }}
                >
                  Công ty
                </div>
                <div
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    fontSize: '12px',
                    color: '#64748b',
                  }}
                >
                  Hoa hồng cho công ty
                </div>
              </div>
            </div>
          );
        }

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Avatar size="small" style={{ backgroundColor: '#3b82f6' }}>
              {user?.name?.charAt(0)?.toUpperCase() || 'D'}
            </Avatar>
            <div>
              <div
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                  fontWeight: 500,
                  color: '#1f2937',
                }}
              >
                {user?.name || 'N/A'}
              </div>
              <div
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                  fontSize: '12px',
                  color: '#64748b',
                }}
              >
                {user?.phone || 'N/A'}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Loại hoa hồng',
      dataIndex: 'type',
      key: 'type',
      width: 140,
      render: (type: string) => (
        <Tooltip
          title={
            type === 'referral'
              ? 'Hoa hồng từ việc giới thiệu khách hàng'
              : 'Hoa hồng thuộc về công ty'
          }
        >
          <Tag
            color={type === 'referral' ? 'blue' : 'green'}
            style={{ fontFamily: "'Be Vietnam Pro', sans-serif", cursor: 'help' }}
          >
            {type === 'referral' ? 'Giới thiệu' : 'Công ty'}
          </Tag>
        </Tooltip>
      ),
    },
    {
      title: 'Tỷ lệ (%)',
      dataIndex: 'percentage',
      key: 'percentage',
      width: 100,
      render: (percentage: number) => (
        <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif", fontWeight: 500 }}>
          {percentage}%
        </span>
      ),
    },
    {
      title: 'Số tiền',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (amount: number) => (
        <span
          style={{ fontFamily: "'Be Vietnam Pro', sans-serif", fontWeight: 500, color: '#1f2937' }}
        >
          {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount)}
        </span>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'orange', text: 'Chờ duyệt' },
          paid: { color: 'green', text: 'Đã duyệt' },
          cancelled: { color: 'red', text: 'Đã hủy' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || {
          color: 'default',
          text: status,
        };
        return (
          <Tag color={config.color} style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => (
        <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif", color: '#64748b' }}>
          {new Date(date).toLocaleDateString('vi-VN')}
        </span>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_: any, record: Commission) => (
        <Space>
          <Tooltip title="Chỉnh sửa hoa hồng">
            <AntButton
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditCommission(record)}
              disabled={record.status !== 'pending'}
            />
          </Tooltip>
          <Tooltip title="Xem lịch sử thay đổi">
            <AntButton
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewHistory(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      if (search !== '') {
        fetchCommissions(1, search, status, dateRange);
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Commission Management */}
        <Card>
          <PageHeader
            title="Lịch sử hoa hồng"
            description="Xem và quản lý lịch sử hoa hồng của các đại lý"
            actions={
              <Space>
                <Button onClick={handleExport} $variant="outline">
                  <Download />
                  Xuất Excel
                </Button>
                <Button
                  onClick={() => {
                    fetchCommissions();
                    fetchStatistics();
                  }}
                  $variant="outline"
                >
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </Space>
            }
          />

          <CardContent>
            {/* Filters Section */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm theo tên đại lý, mã đơn hàng..."
                value={search}
                onChange={handleSearch}
              />

              <FilterGroup>
                <FilterLabel>Trạng thái:</FilterLabel>
                <SelectInput
                  value={status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">Tất cả</option>
                  <option value="pending">Chờ duyệt</option>
                  <option value="paid">Đã thanh toán</option>
                  <option value="cancelled">Đã hủy</option>
                </SelectInput>
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>Thời gian:</FilterLabel>
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  format="DD/MM/YYYY"
                  placeholder={['Từ ngày', 'Đến ngày']}
                  style={{ width: 250 }}
                />
              </FilterGroup>
            </FiltersSection>

            {/* Commissions Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={commissions}
                rowKey="id"
                pagination={{
                  current: page,
                  pageSize: PAGE_SIZE,
                  total: total,
                  onChange: (newPage) => setPage(newPage),
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} hoa hồng`,
                }}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                          {loading ? 'Đang tải dữ liệu...' : 'Không có dữ liệu hoa hồng'}
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </StyledTable>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <span style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                      {loading ? 'Đang tải dữ liệu...' : 'Không có dữ liệu hoa hồng'}
                    </span>
                  }
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Commission History Modal */}
        <Modal
          title="Lịch sử thay đổi hoa hồng"
          open={historyModalVisible}
          onCancel={() => setHistoryModalVisible(false)}
          footer={[
            <AntButton key="close" onClick={() => setHistoryModalVisible(false)}>
              Đóng
            </AntButton>,
          ]}
          width={800}
        >
          {auditLogs.length === 0 ? (
            <Empty
              description="Chưa có lịch sử thay đổi nào"
              style={{ margin: '40px 0', fontFamily: "'Be Vietnam Pro', sans-serif" }}
            />
          ) : (
            <AntTable
              dataSource={auditLogs}
              rowKey="id"
              pagination={false}
              size="small"
              style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
              columns={[
                {
                  title: 'Thời gian',
                  dataIndex: 'createdAt',
                  key: 'createdAt',
                  width: 150,
                  render: (date: string) => (
                    <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                      {new Date(date).toLocaleString('vi-VN')}
                    </span>
                  ),
                },
                {
                  title: 'Trường',
                  dataIndex: 'field',
                  key: 'field',
                  width: 100,
                  render: (field: string) => {
                    const fieldNames: { [key: string]: string } = {
                      amount: 'Số tiền',
                      percentage: 'Tỷ lệ',
                      statusPaid: 'Trạng thái',
                    };
                    return (
                      <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                        {fieldNames[field] || field}
                      </span>
                    );
                  },
                },
                {
                  title: 'Giá trị cũ',
                  dataIndex: 'oldValue',
                  key: 'oldValue',
                  width: 120,
                  render: (value: string, record: any) => (
                    <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif", color: '#ef4444' }}>
                      {record.field === 'percentage' ? `${value}%` : value}
                    </span>
                  ),
                },
                {
                  title: 'Giá trị mới',
                  dataIndex: 'newValue',
                  key: 'newValue',
                  width: 120,
                  render: (value: string, record: any) => (
                    <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif", color: '#10b981' }}>
                      {record.field === 'percentage' ? `${value}%` : value}
                    </span>
                  ),
                },
                {
                  title: 'Người thay đổi',
                  dataIndex: ['changedBy', 'firstname'],
                  key: 'changedBy',
                  width: 150,
                  render: (firstname: string, record: any) => (
                    <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                      {firstname && record.changedBy?.lastname
                        ? `${firstname} ${record.changedBy.lastname}`
                        : firstname || record.changedBy?.username || 'N/A'}
                    </span>
                  ),
                },
                {
                  title: 'Lý do',
                  dataIndex: 'reason',
                  key: 'reason',
                  render: (reason: string) => (
                    <span style={{ fontFamily: "'Be Vietnam Pro', sans-serif", color: '#64748b' }}>
                      {reason || 'Không có lý do'}
                    </span>
                  ),
                },
              ]}
            />
          )}
        </Modal>

        {/* Edit Commission Modal */}
        <Modal
          title="Chỉnh sửa hoa hồng"
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          footer={null}
          width={500}
        >
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleEditSubmit}
            style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
          >
            {selectedCommission && (
              <div
                style={{
                  marginBottom: '16px',
                  padding: '12px',
                  backgroundColor: '#f8fafc',
                  borderRadius: '8px',
                }}
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                      Mã đơn hàng
                    </div>
                    <div style={{ fontWeight: 500, color: '#1f2937' }}>
                      {selectedCommission.order.code}
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                      Giá trị đơn hàng
                    </div>
                    <div style={{ fontWeight: 500, color: '#1f2937' }}>
                      {new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND',
                      }).format(selectedCommission.order.priceAfterTax)}
                    </div>
                  </Col>
                </Row>
                <Row gutter={16} style={{ marginTop: '8px' }}>
                  <Col span={12}>
                    <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                      Người nhận hoa hồng
                    </div>
                    <div style={{ fontWeight: 500, color: '#1f2937' }}>
                      {selectedCommission.type === 'referral' ? (
                        <div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Avatar size="small" style={{ backgroundColor: '#3b82f6' }}>
                              {selectedCommission.user?.name?.charAt(0)?.toUpperCase() || 'D'}
                            </Avatar>
                            <div>
                              <div style={{ fontWeight: 500 }}>
                                {selectedCommission.user?.name || 'N/A'}
                              </div>
                              <div style={{ fontSize: '11px', color: '#64748b' }}>
                                {selectedCommission.user?.phone || 'N/A'}
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <Avatar size="small" style={{ backgroundColor: '#10b981' }}>
                            C
                          </Avatar>
                          <div>
                            <div style={{ fontWeight: 500 }}>Công ty</div>
                            <div style={{ fontSize: '11px', color: '#64748b' }}>
                              Hoa hồng cho công ty
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                      Loại hoa hồng
                    </div>
                    <div style={{ fontWeight: 500, color: '#1f2937' }}>
                      <Tag
                        color={selectedCommission.type === 'referral' ? 'blue' : 'green'}
                        style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
                      >
                        {selectedCommission.type === 'referral' ? 'Giới thiệu' : 'Công ty'}
                      </Tag>
                    </div>
                  </Col>
                </Row>
              </div>
            )}

            <Form.Item
              label="Tỷ lệ hoa hồng (%)"
              name="percentage"
              rules={[
                { required: true, message: 'Vui lòng nhập tỷ lệ hoa hồng' },
                { type: 'number', min: 0, max: 100, message: 'Tỷ lệ phải từ 0 đến 100%' },
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="Nhập tỷ lệ hoa hồng"
                min={0}
                max={100}
                step={0.1}
                onChange={handlePercentageChange}
                addonAfter="%"
              />
            </Form.Item>

            <div
              style={{
                marginBottom: '16px',
                padding: '12px',
                backgroundColor: '#f0f9ff',
                borderRadius: '8px',
              }}
            >
              <div style={{ fontSize: '12px', color: '#0369a1', marginBottom: '4px' }}>
                Số tiền hoa hồng dự kiến
              </div>
              <div style={{ fontSize: '16px', fontWeight: 600, color: '#0369a1' }}>
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
                  calculatedAmount
                )}
              </div>
            </div>

            <Form.Item
              label="Lý do thay đổi"
              name="reason"
              rules={[{ required: true, message: 'Vui lòng nhập lý do thay đổi' }]}
            >
              <Input.TextArea
                rows={3}
                placeholder="Nhập lý do thay đổi tỷ lệ hoa hồng..."
                style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
              />
            </Form.Item>

            <div
              style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px', marginTop: '24px' }}
            >
              <AntButton onClick={() => setEditModalVisible(false)}>Hủy</AntButton>
              <AntButton type="primary" htmlType="submit">
                Cập nhật
              </AntButton>
            </div>
          </Form>
        </Modal>
      </Spin>
    </PageContainer>
  );
};

export default CommissionList;
