import type { Core } from '@strapi/strapi';

const commissionController = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listCommissions(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('commission-service')
      .listCommissions(ctx.query);
  },

  async getCommissionStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('commission-service')
      .getCommissionStatistics(ctx.query);
  },

  async updateCommission(ctx) {
    try {
      const { id } = ctx.params;
      const { percentage, reason } = ctx.request.body;
      const adminUser = ctx.state.user;

      const result = await strapi
        .plugin('management')
        .service('commission-service')
        .updateCommission(Number(id), { percentage, reason }, adminUser);

      ctx.body = {
        success: true,
        data: result,
        message: 'Cập nhật hoa hồng thành công',
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },
});

export default commissionController;
