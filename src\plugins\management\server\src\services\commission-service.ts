import type { Core } from '@strapi/strapi';

const commissionService = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listCommissions(query: any) {
    try {
      const { page = 1, pageSize = 10, status, search, startDate, endDate } = query;

      // Build filters
      const filters: any = {};

      if (status) {
        filters.statusPaid = status;
      }

      if (startDate && endDate) {
        filters.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate + 'T23:59:59.999Z'),
        };
      } else if (startDate) {
        filters.createdAt = { $gte: new Date(startDate) };
      } else if (endDate) {
        filters.createdAt = { $lte: new Date(endDate + 'T23:59:59.999Z') };
      }

      // Build search filters
      let searchFilters = {};
      if (search) {
        searchFilters = {
          $or: [
            { user: { name: { $containsi: search } } },
            { user: { phone: { $containsi: search } } },
            { user: { email: { $containsi: search } } },
            { order: { code: { $containsi: search } } },
          ],
        };
      }

      const finalFilters = search ? { ...filters, ...searchFilters } : filters;

      // Get commissions with pagination
      const commissions = await strapi.entityService.findMany('api::hoa-hong.hoa-hong', {
        filters: finalFilters,
        populate: {
          user: {
            fields: ['id', 'name', 'phone', 'email'],
          },
          order: {
            fields: ['id', 'code', 'priceAfterTax'],
          },
        },
        sort: { createdAt: 'desc' },
        start: (page - 1) * pageSize,
        limit: pageSize,
      });

      // Get total count
      const total = await strapi.db.query('api::hoa-hong.hoa-hong').count({
        where: finalFilters,
      });

      // Format response data
      const formattedCommissions = commissions.map((commission: any) => ({
        id: commission.id,
        amount: commission.amount,
        percentage: commission.percentage,
        status: commission.statusPaid,
        type: commission.type,
        user: {
          id: commission.user?.id || null,
          name: commission.user?.name || 'N/A',
          phone: commission.user?.phone || 'N/A',
          email: commission.user?.email || 'N/A',
        },
        order: {
          id: commission.order?.id || null,
          code: commission.order?.code || 'N/A',
          priceAfterTax: commission.order?.priceAfterTax || 0,
        },
        createdAt: commission.createdAt,
        updatedAt: commission.updatedAt,
      }));

      return {
        data: formattedCommissions,
        meta: {
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            pageCount: Math.ceil(total / pageSize),
            total,
          },
        },
      };
    } catch (error) {
      console.error('Error in listCommissions:', error);
      throw error;
    }
  },

  async getCommissionStatistics(query: any) {
    try {
      // Get all commissions
      const allCommissions = await strapi.entityService.findMany('api::hoa-hong.hoa-hong', {
        populate: ['order'],
      });

      // Calculate statistics
      const stats = {
        totalCommissions: allCommissions.length,
        pendingCommissions: allCommissions.filter((c: any) => c.statusPaid === 'pending').length,
        approvedCommissions: allCommissions.filter((c: any) => c.statusPaid === 'paid').length,
        paidCommissions: allCommissions.filter((c: any) => c.statusPaid === 'paid').length,
        totalAmount: allCommissions.reduce((sum: number, c: any) => sum + (c.amount || 0), 0),
        pendingAmount: allCommissions
          .filter((c: any) => c.statusPaid === 'pending')
          .reduce((sum: number, c: any) => sum + (c.amount || 0), 0),
        approvedAmount: allCommissions
          .filter((c: any) => c.statusPaid === 'paid')
          .reduce((sum: number, c: any) => sum + (c.amount || 0), 0),
        paidAmount: allCommissions
          .filter((c: any) => c.statusPaid === 'paid')
          .reduce((sum: number, c: any) => sum + (c.amount || 0), 0),
      };

      return stats;
    } catch (error) {
      console.error('Error in getCommissionStatistics:', error);
      throw error;
    }
  },

  async updateCommission(id: number, updateData: any, adminUser: any) {
    try {
      // Get current commission
      const currentCommission = await strapi.entityService.findOne('api::hoa-hong.hoa-hong', id, {
        populate: ['order'],
      });

      if (!currentCommission) {
        throw new Error('Không tìm thấy hoa hồng');
      }

      // Calculate new amount based on percentage
      const orderTotal = currentCommission.order?.priceAfterTax || 0;
      const newAmount = Math.round((orderTotal * updateData.percentage) / 100);

      // Update commission
      const updatedCommission = await strapi.db.query('api::hoa-hong.hoa-hong').update({
        where: { id },
        data: {
          percentage: updateData.percentage,
          amount: newAmount,
        },
      });

      // Create audit log
      await strapi.db.query('api::hoa-hong-audit.hoa-hong-audit').create({
        data: {
          hoaHong: id,
          action: 'update',
          field: 'percentage',
          oldValue: currentCommission.percentage.toString(),
          newValue: updateData.percentage.toString(),
          changedBy: adminUser.id,
          reason: updateData.reason || 'Cập nhật tỷ lệ hoa hồng',
        },
      });

      return updatedCommission;
    } catch (error) {
      console.error('Error in updateCommission:', error);
      throw error;
    }
  },
});

export default commissionService;
